import { Accordion, <PERSON><PERSON>, Typography } from "@apollo/ui"

<Alert
  type="information"
  fullWidth
  description="This article explains what's changing and what's coming next for our Design System."
/>

# A Quick Intro

First off, let's remember this phrase:

## "Old stuff still works, new stuff moves forward"

### What this means

**@design-systems/apollo-ui (v1)**

- Will continue to work as usual
- Will still receive bug fixes
- But no new features will be added

**@apollo/ui (v2)**

- This is the version we'll be developing and adding new features to going forward
- Making it as stable as possible so developers can work faster

<Alert
  type="warning"
  fullWidth
  description="⚠️ TL;DR: No need to remove v1. Just install v2 alongside it."
/>

<br />

---

## ⚙️ What Happened with v1?

### Feedback We Got from Developers

Here's the feedback we received from developers using Apollo v1:

- 😞 Using Apollo v1 made building UI slower instead of faster
- 😞 Component designs didn't match Figma, requiring extra customization
- 😞 Wanted to update React version but couldn't because libraries (including Apollo) were too old
- 😞 Various other annoying issues

### Technical Problems

- Couldn't work with older or newer versions of React
- Forced projects to use React 18.2 or higher
- Required Tailwind CSS as the main styling library

<br />

---

## 🚀 Introducing @apollo/ui (v2)

### Main Changes

#### 🎨 No Longer Tied to Tailwind CSS

- Uses CSS Modules instead (projects just import CSS and start using it)
- Each project has the freedom to choose their own styling library (we provide Design Tokens)

#### 🖼️ Easier Theming (M3 Inspired)

- Supports Dark/Light Mode

#### 🏭 Changed Bundler from Rollup to Vite + React Compiler

- Works with React 17 all the way to 19+ (old version only worked with React 18.x)
- Reduced unnecessary plugins for faster bundling

#### ⚡ More Consistent Props API

- Many component Props APIs have been improved for consistency

#### ✅ Design Matches Figma Better

- In v2, we've changed how we work with designers
- This makes the final design match Figma much better

#### ✨ New Components

- New components coming in the future, like DataTable (with rich features)
- To help developers work faster

#### ✨ Integration with Figma Code Connect (Coming Soon)

- In the AI era, Figma now has something called MCP Server
- AI can pull code examples directly from Figma
- Reduces the need to open documentation to understand components

<br />

---

## 📌 What's Next

<Alert
  type="error"
  fullWidth
  description="When bugs occur, instead of fixing them based on priority alongside our other work outside of Design System,

All bugs found in v2 will be treated as URGENT Bugs that must be fixed immediately to ensure the smoothest experience possible."
/>

<br />

### We Invite You to Install v2

We invite everyone to install `@apollo/ui` (v2) in your projects and start using it whenever possible.

UX/UI Designers (Figma) and UI-Engineers (React Library) have adjusted our process and set new guidelines to keep UI consistent.

**If you find UI that doesn't match Figma, please let us know!**

<br />

### How to Tell Which Version a Figma File Is

In Figma, there's an indicator showing which version the file is:

**Page Level Badge:**

<img
  style={{ maxWidth: "300px" }}
  src="/v2-article/page-level-badge.png"
  alt="Page Level Badge"
/>

**Or Part Level Badge:**

<img
  style={{ maxWidth: "300px" }}
  src="/v2-article/part-level-badge.png"
  alt="Part Level Badge"
/>

<br />

When you see these, it means the file uses @apollo/ui (v2)

<br />

### Accessing v2 Documentation

Documentation is located in these two sections on the left sidebar:

<img src="/v2-article/v2-section.png" style={{ maxWidth: "300px" }} />

Or go directly to [v2 Documentation](/docs/apollo∕ui-introduction--docs)

<br />

---

## ❓ Frequently Asked Questions (FAQ)

<Accordion label="What will happen to v1?" onOpenChange={() => {}}>
  <Typography level="bodyMedium">
    v1 will continue to work normally. We just won't be adding new features
    anymore. We'll only support bug fixes.
  </Typography>
</Accordion>

<br />

<Accordion label="Can I not use v2?" onOpenChange={() => {}}>
  <Typography level="bodyMedium">
    Yes, you can. However, since UX/UI will mainly use v2 in new Figma files,
    every time we want to build UI and inspect designs, we'll need to compare
    them with v1. Plus, some things in v2 won't exist in v1, requiring
    additional customization.
  </Typography>
</Accordion>

<br />

<Accordion label="Wait, will this change again in the future?" onOpenChange={() => {}}>
  <div>
    <Typography level="bodyMedium">
      First of all, we sincerely apologize for these changes that require you to keep adapting.
    </Typography>

    <Typography level="headingSmall" style={{ margin: "16px 0" }}>
      The answer is "No more changes. We promise 🙏"
    </Typography>

    <Typography level="bodyMedium">
      We've thought this through. If we kept using v1, the problems would only get bigger due to v1's many limitations. Adding new features or making changes becomes increasingly difficult.
    </Typography>

    <Typography level="bodyMedium" >
      So in summary, @apollo/ui will be the main library we'll develop and add new features to for the long term.
    </Typography>

  </div>
</Accordion>

<br />

<Accordion
  label="What if I only want to update some components on a page?"
  onOpenChange={() => {}}
>
  <div>
    <Typography level="bodyMedium">
      We understand that sometimes you can't switch from v1 to v2 all at once.
    </Typography>

    <Typography level="bodyMedium" >
      You can import components from @apollo/ui (v2) and use them alongside components from @design-systems/apollo-ui (v1). We have a way to make v2 components look as close to v1 as possible. You can follow these steps{" "}
      <a href="/docs/apollo∕ui-switch-to-ui-styles-of-v1--docs">
        👉 Click here
      </a>
    </Typography>

  </div>
</Accordion>

<br />

<Accordion
  label="What can I do if I find UI that doesn't match?"
  onOpenChange={() => {}}
>
  <div>
    <Typography level="bodyMedium">
      In this case, we'd like everyone to report bugs to us. Let's work together to make our Design System better.
    </Typography>
    <Typography level="bodyMedium" >
      If you find UI that doesn't match Figma, something definitely went wrong. You can contact a UI-Engineer or inform the UX/UI team for your project.
    </Typography>

  </div>
</Accordion>

<br />
