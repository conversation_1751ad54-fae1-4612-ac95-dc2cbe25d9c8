import React, { useState } from "react"
import { UsageGuidelines } from "@/components"
import { BottomSheetContent } from "@apollo/storefront"
import { Button, Input, Typography } from "@apollo/ui"
import {
  Heart,
  Shopping,
  Star,
} from "@design-systems/apollo-icons"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { Meta, StoryObj } from "@storybook/react"

import "@apollo/storefront/style.css"

/**
 * BottomSheetContent component
 *
 * The BottomSheetContent component provides a structured layout with a scrollable
 * content area and optional sticky footer. It can be used independently in any
 * container or within a BottomSheet for consistent content organization.
 *
 * Notes:
 * - Automatically handles scrolling for content that exceeds the available space
 * - Optional footer that remains sticky at the bottom
 * - Flexible layout that adapts to different content types and container sizes
 * - Optimized for mobile touch scrolling with -webkit-overflow-scrolling
 * - Can be used as a standalone layout component in any context
 */
const meta = {
  title: "@apollo∕storefront/Components/Data Display/BottomSheetContent",
  component: BottomSheetContent,
  tags: ["autodocs", "new"],
  globals: {
    brand: "storefront",
  },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/design/1Ufffyr7D28j6MXLQDLSro/%F0%9F%92%9A-Apollo-Alias-Storefront?node-id=6076-13562&m=dev",
    },
    docs: {
      description: {
        component:
          "The BottomSheetContent component provides a structured layout with scrollable content area and optional sticky footer. Can be used independently or within BottomSheet.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source
            code={`import { BottomSheetContent } from "@apollo/storefront"`}
            language="tsx"
          />
          <h2 id="bottomsheetcontent-props">Props</h2>
          <ArgTypes />
          <h2 id="bottomsheetcontent-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Use BottomSheetContent for bottom sheet content layout with optional sticky footer",
              "Content area automatically handles overflow with smooth scrolling behavior",
              "Use proper semantic HTML structure within children for accessibility",
              "Keep footer content concise - typically 1-2 action buttons work best",
            ]}
          />
          <h2 id="bottomsheetcontent-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              <>
                The component maintains proper scrolling behavior for assistive
                technologies with touch scrolling optimization.
              </>,
              <>
                Use semantic HTML elements within children for better screen
                reader understanding.
              </>,
              <>
                Use <code>footer</code> prop for actions that should remain visible while scrolling.
              </>,
            ]}
          />
          <h2 id="bottomsheetcontent-examples">Examples</h2>
          <Stories title="" />
        </>
      ),
    },
  },
  argTypes: {
    children: {
      control: false,
      description: "The main content to display in the scrollable area.",
      table: {
        type: { summary: "ReactNode" },
        defaultValue: { summary: "-" },
      },
    },
    footer: {
      control: false,
      description: "Optional footer content that remains sticky at the bottom.",
      table: {
        type: { summary: "ReactNode" },
        defaultValue: { summary: "-" },
      },
    },
    className: {
      control: { type: "text" },
      description: "Additional CSS class names to apply to the root element.",
      table: {
        type: { summary: "string" },
        defaultValue: { summary: "-" },
      },
    },
  },
  args: {},
} satisfies Meta<typeof BottomSheetContent>

export default meta

type Story = StoryObj<typeof BottomSheetContent>

/** Default BottomSheetContent (demonstrates basic content layout) */
export const Overview: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Basic BottomSheetContent with simple content. Shows how the component structures content in a scrollable area without any footer.",
      },
    },
  },
  render: function OverviewDemo(args) {
    return (
      <div style={{ padding: 20 }}>
        <Typography level="bodyMedium" style={{ marginBottom: 16 }}>
          BottomSheetContent as a standalone layout component:
        </Typography>

        <div
          style={{
            height: 300,
            border: "2px solid #e0e0e0",
            borderRadius: 8,
            backgroundColor: "#fff",
            overflow: "hidden",
          }}
        >
          <BottomSheetContent {...args}>
            <div style={{ padding: 16 }}>
              <Typography level="titleLarge" style={{ marginBottom: 16 }}>
                Welcome to BottomSheetContent
              </Typography>
              <Typography level="bodyMedium" style={{ marginBottom: 16 }}>
                This component provides a structured layout with a scrollable content area.
                It can be used independently in any container, not just within BottomSheet.
              </Typography>
              <Typography level="bodyMedium" style={{ marginBottom: 16 }}>
                You can include any type of content here - text, forms, lists, images, or
                interactive elements. The layout automatically adapts to your content.
              </Typography>
              <Typography level="bodyMedium" style={{ marginBottom: 16 }}>
                The component ensures proper scrolling behavior and maintains accessibility
                standards for mobile and desktop experiences.
              </Typography>
              <Typography level="bodyMedium">
                Try adding more content to see how the scrolling behavior works within
                the constrained container height.
              </Typography>
            </div>
          </BottomSheetContent>
        </div>
      </div>
    )
  },
}

/** BottomSheetContent with footer for actions */
export const WithFooter: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "BottomSheetContent with a sticky footer containing action buttons. The footer remains visible while the content area scrolls.",
      },
    },
  },
  render: function WithFooterDemo() {
    const [formData, setFormData] = useState({ name: "", email: "", message: "" })

    const handleSubmit = () => {
      console.log("Form submitted:", formData)
      setFormData({ name: "", email: "", message: "" })
    }

    const footer = (
      <div style={{ display: "flex", gap: 12, padding: 16, borderTop: "1px solid #e0e0e0" }}>
        <Button
          variant="outline"
          onClick={() => setFormData({ name: "", email: "", message: "" })}
          style={{ flex: 1 }}
        >
          Reset
        </Button>
        <Button
          onClick={handleSubmit}
          style={{ flex: 1 }}
          disabled={!formData.name || !formData.email}
        >
          Submit
        </Button>
      </div>
    )

    return (
      <div style={{ padding: 20 }}>
        <Typography level="bodyMedium" style={{ marginBottom: 16 }}>
          BottomSheetContent with sticky footer - notice how the footer stays at the bottom:
        </Typography>

        <div
          style={{
            height: 400,
            border: "2px solid #e0e0e0",
            borderRadius: 8,
            backgroundColor: "#fff",
            overflow: "hidden",
          }}
        >
          <BottomSheetContent footer={footer}>
            <div style={{ padding: 16 }}>
              <Typography level="titleMedium" style={{ marginBottom: 16 }}>
                Contact Form
              </Typography>
              <Typography level="bodyMedium" style={{ marginBottom: 24 }}>
                Fill out the form below. All fields marked with * are required.
              </Typography>

              <div style={{ display: "flex", flexDirection: "column", gap: 16 }}>
                <div>
                  <Typography level="bodyMedium" style={{ marginBottom: 8, fontWeight: 600 }}>
                    Name *
                  </Typography>
                  <Input
                    placeholder="Enter your full name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    fullWidth
                  />
                </div>

                <div>
                  <Typography level="bodyMedium" style={{ marginBottom: 8, fontWeight: 600 }}>
                    Email *
                  </Typography>
                  <Input
                    type="email"
                    placeholder="Enter your email address"
                    value={formData.email}
                    onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                    fullWidth
                  />
                </div>

                <div>
                  <Typography level="bodyMedium" style={{ marginBottom: 8, fontWeight: 600 }}>
                    Message
                  </Typography>
                  <textarea
                    placeholder="Enter your message (optional)"
                    value={formData.message}
                    onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
                    style={{
                      width: "100%",
                      minHeight: 100,
                      padding: 12,
                      border: "1px solid #ccc",
                      borderRadius: 8,
                      fontFamily: "inherit",
                      fontSize: "inherit",
                      resize: "vertical",
                    }}
                  />
                </div>

                <div>
                  <Typography level="bodyMedium" style={{ marginBottom: 8, fontWeight: 600 }}>
                    Additional Information
                  </Typography>
                  <Typography level="bodySmall" style={{ marginBottom: 16, color: "#666" }}>
                    This extra content demonstrates how the footer remains sticky at the bottom
                    while the content area scrolls when it exceeds the available space.
                  </Typography>
                </div>
              </div>
            </div>
          </BottomSheetContent>
        </div>
      </div>
    )
  },
}

/** BottomSheetContent with scrollable content */
export const ScrollableContent: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "BottomSheetContent with long scrollable content. Demonstrates how the component handles overflow and maintains smooth scrolling behavior.",
      },
    },
  },
  render: function ScrollableContentDemo() {
    const getIcon = (index: number) => {
      if (index % 3 === 0) return <Star width={20} height={20} />
      if (index % 3 === 1) return <Heart width={20} height={20} />
      return <Shopping width={20} height={20} />
    }

    const items = Array.from({ length: 15 }, (_, i) => ({
      id: i + 1,
      title: `Item ${i + 1}`,
      description: `This is the description for item ${i + 1}. It contains some sample text to demonstrate content layout and scrolling behavior.`,
      icon: getIcon(i),
    }))

    return (
      <div style={{ padding: 20 }}>
        <Typography level="bodyMedium" style={{ marginBottom: 16 }}>
          BottomSheetContent with scrollable content - scroll within the container to see more items:
        </Typography>

        <div
          style={{
            height: 350,
            border: "2px solid #e0e0e0",
            borderRadius: 8,
            backgroundColor: "#fff",
            overflow: "hidden",
          }}
        >
          <BottomSheetContent>
            <div style={{ padding: 16 }}>
              <Typography level="titleMedium" style={{ marginBottom: 16 }}>
                Scrollable Item List
              </Typography>
              <Typography level="bodyMedium" style={{ marginBottom: 16 }}>
                This list contains many items to demonstrate scrolling behavior.
                The content area automatically handles overflow with smooth scrolling.
              </Typography>

              <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
                {items.map((item) => (
                  <div
                    key={item.id}
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: 12,
                      padding: 16,
                      border: "1px solid #eee",
                      borderRadius: 8,
                      backgroundColor: "#fafafa",
                    }}
                  >
                    <div style={{ color: "var(--apl-alias-color-primary-primary)" }}>
                      {item.icon}
                    </div>
                    <div style={{ flex: 1 }}>
                      <Typography level="bodyMedium" style={{ fontWeight: 600, marginBottom: 4 }}>
                        {item.title}
                      </Typography>
                      <Typography level="bodySmall" style={{ color: "#666" }}>
                        {item.description}
                      </Typography>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </BottomSheetContent>
        </div>
      </div>
    )
  },
}

/** BottomSheetContent with mixed content types and footer */
export const MixedContentWithFooter: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "BottomSheetContent showcasing various content types including text, images, forms, and interactive elements with a sticky footer.",
      },
    },
  },
  render: function MixedContentDemo() {
    const [rating, setRating] = useState(0)
    const [selectedSize, setSelectedSize] = useState("M")

    const sizes = ["XS", "S", "M", "L", "XL"]

    const footer = (
      <div style={{ display: "flex", gap: 12, padding: 16, borderTop: "1px solid #e0e0e0" }}>
        <Button
          variant="outline"
          onClick={() => {
            setRating(0)
            setSelectedSize("M")
          }}
          style={{ flex: 1 }}
        >
          Reset
        </Button>
        <Button
          onClick={() => {
            console.log("Added to cart:", { rating, selectedSize })
          }}
          style={{ flex: 1 }}
        >
          Add to Cart
        </Button>
      </div>
    )

    return (
      <div style={{ padding: 20 }}>
        <Typography level="bodyMedium" style={{ marginBottom: 16 }}>
          BottomSheetContent with mixed content types and sticky footer:
        </Typography>

        <div
          style={{
            height: 500,
            border: "2px solid #e0e0e0",
            borderRadius: 8,
            backgroundColor: "#fff",
            overflow: "hidden",
          }}
        >
          <BottomSheetContent footer={footer}>
            <div style={{ padding: 16 }}>
              {/* Product Image Placeholder */}
              <div
                style={{
                  width: "100%",
                  height: 150,
                  backgroundColor: "#f0f0f0",
                  borderRadius: 8,
                  marginBottom: 20,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  border: "2px dashed #ccc",
                }}
              >
                <Typography level="bodyMedium" style={{ color: "#666" }}>
                  Product Image Placeholder
                </Typography>
              </div>

              {/* Product Info */}
              <div style={{ marginBottom: 20 }}>
                <Typography level="titleMedium" style={{ marginBottom: 8 }}>
                  Premium Cotton T-Shirt
                </Typography>
                <Typography level="bodyLarge" style={{ color: "var(--apl-alias-color-primary-primary)", fontWeight: 600, marginBottom: 12 }}>
                  $29.99
                </Typography>
                <Typography level="bodyMedium" style={{ lineHeight: 1.6, marginBottom: 12 }}>
                  Made from 100% organic cotton, this comfortable t-shirt features a classic fit
                  and premium quality construction.
                </Typography>
              </div>

              {/* Size Selection */}
              <div style={{ marginBottom: 20 }}>
                <Typography level="bodyMedium" style={{ fontWeight: 600, marginBottom: 12 }}>
                  Size
                </Typography>
                <div style={{ display: "flex", gap: 8, flexWrap: "wrap" }}>
                  {sizes.map((size) => (
                    <button
                      key={size}
                      onClick={() => setSelectedSize(size)}
                      style={{
                        padding: "8px 16px",
                        border: `2px solid ${selectedSize === size ? "var(--apl-alias-color-primary-primary)" : "#ccc"}`,
                        borderRadius: 6,
                        backgroundColor: selectedSize === size ? "var(--apl-alias-color-primary-primary-container)" : "transparent",
                        cursor: "pointer",
                        fontWeight: selectedSize === size ? 600 : 400,
                      }}
                    >
                      {size}
                    </button>
                  ))}
                </div>
              </div>

              {/* Rating */}
              <div style={{ marginBottom: 20 }}>
                <Typography level="bodyMedium" style={{ fontWeight: 600, marginBottom: 12 }}>
                  Rate this product
                </Typography>
                <div style={{ display: "flex", gap: 4 }}>
                  {[1, 2, 3, 4, 5].map((star) => (
                    <button
                      key={star}
                      onClick={() => setRating(star)}
                      style={{
                        background: "none",
                        border: "none",
                        cursor: "pointer",
                        padding: 4,
                      }}
                    >
                      <Star
                        width={20}
                        height={20}
                        style={{
                          color: star <= rating ? "var(--apl-alias-color-warning-warning)" : "#ccc",
                        }}
                      />
                    </button>
                  ))}
                </div>
              </div>

              {/* Features List */}
              <div>
                <Typography level="bodyMedium" style={{ fontWeight: 600, marginBottom: 12 }}>
                  Features
                </Typography>
                <ul style={{ paddingLeft: 20, margin: 0 }}>
                  <li style={{ marginBottom: 8 }}>
                    <Typography level="bodyMedium">100% organic cotton</Typography>
                  </li>
                  <li style={{ marginBottom: 8 }}>
                    <Typography level="bodyMedium">Machine washable</Typography>
                  </li>
                  <li style={{ marginBottom: 8 }}>
                    <Typography level="bodyMedium">Classic fit design</Typography>
                  </li>
                  <li>
                    <Typography level="bodyMedium">Available in multiple colors</Typography>
                  </li>
                </ul>
              </div>
            </div>
          </BottomSheetContent>
        </div>
      </div>
    )
  },
}

/** BottomSheetContent with custom className */
export const CustomStyling: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "BottomSheetContent with custom className to demonstrate styling flexibility. Shows how the component can be customized for different design needs.",
      },
    },
  },
  render: function CustomStylingDemo() {
    return (
      <div style={{ padding: 20 }}>
        <Typography level="bodyMedium" style={{ marginBottom: 16 }}>
          BottomSheetContent with custom styling applied via className:
        </Typography>

        <div
          style={{
            height: 300,
            border: "2px solid #e0e0e0",
            borderRadius: 8,
            backgroundColor: "#fff",
            overflow: "hidden",
          }}
        >
          <BottomSheetContent
            className="custom-bottom-sheet-content"
            style={{
              background: "linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)",
            }}
          >
            <div style={{ padding: 16 }}>
              <Typography level="titleMedium" style={{ marginBottom: 16 }}>
                Custom Styled Content
              </Typography>
              <Typography level="bodyMedium" style={{ marginBottom: 16 }}>
                This example shows how BottomSheetContent can be customized with
                custom CSS classes and inline styles.
              </Typography>
              <Typography level="bodyMedium" style={{ marginBottom: 16 }}>
                The component accepts all standard HTML div attributes, making it
                flexible for various design requirements.
              </Typography>
              <Typography level="bodyMedium">
                You can apply custom backgrounds, borders, shadows, or any other
                styling while maintaining the core layout functionality.
              </Typography>
            </div>
          </BottomSheetContent>
        </div>
      </div>
    )
  },
}
