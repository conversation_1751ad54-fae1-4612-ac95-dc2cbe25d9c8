import React, { useState } from "react"
import { ComponentRules, UsageGuidelines } from "@/components"
import { BottomSheet, BottomSheetContent } from "@apollo/storefront"
import { Button, Input, Typography } from "@apollo/ui"
import {
  Heart,
  Shopping,
  Star,
} from "@design-systems/apollo-icons"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { Meta, StoryObj } from "@storybook/react"

import "@apollo/storefront/style.css"

const actionDo = "/assets/apollo-storefront/bottom-sheet/action-do.png"
const actionDont = "/assets/apollo-storefront/bottom-sheet/action-dont.png"

/**
 * BottomSheet component
 *
 * The BottomSheet component provides a modal overlay that slides up from the bottom
 * of the screen. It's designed for mobile-first experiences and supports draggable
 * resizing with snap points. Perfect for displaying additional content, forms, or
 * actions without leaving the current context.
 *
 * Notes:
 * - Supports multiple snap points for different content heights
 * - Draggable handle for intuitive resizing
 * - Optional content dragging for enhanced mobile experience
 * - Backdrop click to close functionality
 * - Keyboard navigation support (Escape to close)
 * - Built with accessibility in mind with proper ARIA attributes
 */
const meta = {
  title: "@apollo∕storefront/Components/Data Display/BottomSheet",
  component: BottomSheet,
  tags: ["autodocs", "new"],
  globals: {
    brand: "storefront",
  },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/design/1Ufffyr7D28j6MXLQDLSro/%F0%9F%92%9A-Apollo-Alias-Storefront?node-id=6076-13562&m=dev",
    },
    docs: {
      description: {
        component:
          "The BottomSheet component provides a modal overlay that slides up from the bottom of the screen. Perfect for mobile-first experiences with draggable resizing, snap points, and accessibility features.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source
            code={`import { BottomSheet, BottomSheetContent } from "@apollo/storefront"`}
            language="tsx"
          />
          <h2 id="bottomsheet-props">Props</h2>
          <ArgTypes />
          <h2 id="bottomsheet-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Use BottomSheet for: Secondary actions, forms, filters, and additional content that doesn't require full page navigation",
              "Configure snap points based on your content - typically 30%, 60%, and 90% work well for most use cases",
              "Always provide a title for better accessibility and user understanding",
              "Use BottomSheetContent component for consistent content layout with optional footer",
              "Use onOpenChange to control open state and manage your UI when the sheet opens or closes",
              "For uncontrolled usage, mount/unmount the sheet and pass defaultOpen to show it initially; use onOpenChange to unmount when closed",
              "Consider the backdrop click behavior - users expect it to close the sheet",
            ]}
          />
          <h2 id="bottomsheet-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              <>
                Always provide a descriptive <code>title</code> prop for screen
                reader users to understand the sheet's purpose.
              </>,
              <>
                The component automatically handles focus management and ARIA
                attributes for modal behavior.
              </>,
              <>
                Listen to <code>onOpenChange</code> to update your state when users press Escape
                or click the backdrop for keyboard navigation.
              </>,
              <>
                When using custom <code>closeIcon</code>, ensure it has
                appropriate accessibility labels.
              </>,
              <>
                Use <code>BottomSheetContent</code> component for consistent
                content layout with optional sticky footer.
              </>,
              <>Use <code>allowContentDrag</code> prop to enable content dragging for better mobile experience.</>,
              <>
                Consider the reading order when placing interactive elements
                within the sheet content.
              </>,
            ]}
          />
          <h2 id="bottomsheet-examples">Examples</h2>
          <Stories title="" />
          <h2 id="bottomsheet-dos-donts">Do's and Don'ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div style={{ height: '400px'}}>
                      <img src={actionDo} alt="bottom sheet with clear title and buttons" style={{ maxWidth: "100%", padding: 24, height: '100%' }} />
                    </div>
                  ),
                  description:
                    "Display bottom sheet with backgound of overlay and clear title.",
                },
                negative: {
                  component: (
                    <div style={{ height: '400px'}}>
                      <img src={actionDont} alt="bottom sheet without backgound of overlay" style={{ maxWidth: "100%", padding: 24, height: '100%' }} />
                    </div>
                  ),
                  description:
                    "Avoid bottom sheet without backgound of overlay",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  argTypes: {
    open: {
      control: { type: "boolean" },
      description: "Whether the bottom sheet is open and visible.",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    onOpenChange: {
      control: false,
      description: "Callback fired when the open state changes.",
      table: {
        type: { summary: "(open: boolean) => void" },
        defaultValue: { summary: "-" },
      },
    },
    title: {
      control: { type: "text" },
      description: "Title displayed in the header of the bottom sheet.",
      table: {
        type: { summary: "ReactNode" },
        defaultValue: { summary: "-" },
      },
    },
    closeIcon: {
      control: false,
      description: "Custom close icon to display in the header.",
      table: {
        type: { summary: "ReactNode" },
        defaultValue: { summary: "<CloseIcon />" },
      },
    },
    defaultOpen: {
      control: { type: "boolean" },
      description: "Initial open state for uncontrolled usage.",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    snapPoints: {
      control: { type: "object" },
      description: "Array of snap points (percentages) or single number for fixed height.",
      table: {
        type: { summary: "number[] | number" },
        defaultValue: { summary: "[30, 60, 90]" },
      },
    },
    defaultSnap: {
      control: { type: "number", min: 10, max: 100, step: 5 },
      description: "Default snap point percentage when opening.",
      table: {
        type: { summary: "number" },
        defaultValue: { summary: "60" },
      },
    },
    allowContentDrag: {
      control: { type: "boolean" },
      description: "Whether content area can be dragged to resize the sheet.",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    className: {
      control: { type: "text" },
      description: "Additional CSS class names to apply to the root element.",
      table: {
        type: { summary: "string" },
        defaultValue: { summary: "-" },
      },
    },
  },
  args: {
    title: "Bottom Sheet",
    snapPoints: [30, 60, 90],
    defaultSnap: 60,
    allowContentDrag: false,
  },
} satisfies Meta<typeof BottomSheet>

export default meta

type Story = StoryObj<typeof BottomSheet>

/** Default BottomSheet (demonstrates basic functionality) */
export const Overview: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Overview BottomSheet with default settings. Shows a bottom sheet with title, close button, and basic content. Click the button to open the sheet.",
      },
    },
  },
  render: function OverviewDemo(args) {
    const [isOpen, setIsOpen] = useState(false)

    return (
      <div style={{ padding: 20, minHeight: "100vh" }}>
        <Button onClick={() => setIsOpen(true)}>Open Bottom Sheet</Button>

        <BottomSheet
          {...args}
          open={isOpen}
          onOpenChange={setIsOpen}

        >
          <BottomSheetContent>
            <div style={{ padding: "16px 0" }}>
              <Typography level="bodyLarge" style={{ marginBottom: 16 }}>
                Welcome to the Bottom Sheet
              </Typography>
              <Typography level="bodyMedium" style={{ marginBottom: 16 }}>
                This is a basic bottom sheet example. You can drag the handle at the top
                to resize it, or click the backdrop to close it.
              </Typography>
              <Typography level="bodyMedium">
                The sheet supports multiple snap points and can be configured for
                different use cases.
              </Typography>
            </div>
          </BottomSheetContent>
        </BottomSheet>
      </div>
    )
  },
  args: {
    title: "Basic Bottom Sheet",
  },
}


/** BottomSheet using defaultOpen (uncontrolled via mount/unmount) */
export const UncontrolledDefaultOpen: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates using defaultOpen for an uncontrolled pattern. We mount the sheet when needed and listen to onOpenChange to unmount when users close it.",
      },
    },
  },
  render: function UncontrolledDefaultOpenDemo(args) {
    const [show, setShow] = useState(false)

    return (
      <div style={{ padding: 20, minHeight: "100vh" }}>
        <Button onClick={() => setShow(true)}>Open (defaultOpen)</Button>

        {show && (
          <BottomSheet
            {...args}
            defaultOpen={true}
            onOpenChange={(open) => {
              if (!open) setShow(false)
            }}
            title="Uncontrolled Sheet"
          >
            <BottomSheetContent>
              <div style={{ padding: "16px 0" }}>
                <Typography level="bodyMedium" style={{ display: 'block', marginBottom: 12 }}>
                  This sheet is opened by mounting it with defaultOpen. Closing via backdrop/Escape triggers onOpenChange(false), which unmounts the sheet.
                </Typography>
                <div style={{ display: 'flex', gap: 8 }}>
                  <Input placeholder="Type here" size="small" fullWidth />
                  <Button size="small" onClick={() => setShow(false)}>Done</Button>
                </div>
              </div>
            </BottomSheetContent>
          </BottomSheet>
        )}
      </div>
    )
  },
  args: {
    snapPoints: [30, 60, 90],
    defaultSnap: 60,
  },
}


/** BottomSheet with different snap points and configurations */
export const Variants: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "A comprehensive showcase of BottomSheet variants including different snap points, content dragging, and custom close icons.",
      },
    },
  },
  render: function VariantsDemo() {
    const [activeSheet, setActiveSheet] = useState<string | null>(null)

    const openSheet = (sheetId: string) => setActiveSheet(sheetId)
    const closeSheet = () => setActiveSheet(null)

    return (
      <div style={{ padding: 20, minHeight: "100vh" }}>
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
            gap: 16,
            marginBottom: 32,
          }}
        >
          <Button onClick={() => openSheet("small")}>Small Sheet (30%)</Button>
          <Button onClick={() => openSheet("large")}>Large Sheet (80%)</Button>
          <Button onClick={() => openSheet("draggable")}>Content Draggable</Button>
          <Button onClick={() => openSheet("custom-icon")}>Custom Close Icon</Button>
        </div>

        {/* Small Sheet */}
        <BottomSheet
          open={activeSheet === "small"}
          onOpenChange={(open) => !open && closeSheet()}

          title="Small Bottom Sheet"
          snapPoints={[30]}
          defaultSnap={30}
        >
          <BottomSheetContent>
            <div style={{ padding: "16px 0" }}>
              <Typography level="bodyMedium" style={{ display: 'block'}}>
                This is a smaller bottom sheet that only takes up 30% of the screen height.
                Perfect for quick actions or simple forms.
              </Typography>
            </div>
          </BottomSheetContent>
        </BottomSheet>

        {/* Large Sheet */}
        <BottomSheet
          open={activeSheet === "large"}
          onOpenChange={(open) => !open && closeSheet()}

          title="Large Bottom Sheet"
          snapPoints={[40, 80]}
          defaultSnap={80}
        >
          <BottomSheetContent>
            <div style={{ padding: "16px 0" }}>
              <Typography level="bodyLarge" style={{ marginBottom: 16 }}>
                Large Content Area
              </Typography>
              <Typography level="bodyMedium" style={{ marginBottom: 16, display: 'block' }}>
                This bottom sheet opens at 80% height and can be resized to 40%.
                Great for detailed forms or extensive content.
              </Typography>
              <div style={{ marginBottom: 16 }}>
                <Typography level="bodyMedium" style={{ marginBottom: 8 }}>
                  Sample form content:
                </Typography>
                <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
                  <Input placeholder="Name" fullWidth size="small"/>
                  <Input placeholder="Email" fullWidth size="small"/>
                </div>
              </div>
            </div>
          </BottomSheetContent>
        </BottomSheet>

        {/* Content Draggable Sheet */}
        <BottomSheet
          open={activeSheet === "draggable"}
          onOpenChange={(open) => !open && closeSheet()}

          title="Draggable Content"
          snapPoints={[25, 60, 100]}
          defaultSnap={60}
          allowContentDrag={true}
        >
          <BottomSheetContent>
            <div style={{ padding: "16px 0" }}>
              <Typography level="bodyLarge" style={{ marginBottom: 16 }}>
                Content Dragging Enabled
              </Typography>
              <Typography level="bodyMedium" style={{ marginBottom: 16, display: 'block' }}>
                This sheet allows dragging from the content area when scrolled to the top.
                Try dragging anywhere in the content area to resize.
              </Typography>
              <div style={{ height: 200, overflowY: "auto", border: "1px solid #eee", padding: 16, borderRadius: 8 }}>
                <Typography level="bodyMedium" style={{ marginBottom: 16 }}>
                  Scrollable content area. When you scroll down, dragging is disabled to allow normal scrolling.
                </Typography>
                {Array.from({ length: 10 }, (_, i) => (
                  <Typography key={i} level="bodySmall" style={{ marginBottom: 8 }}>
                    Content line {i + 1} - This is some sample content to demonstrate scrolling behavior.
                  </Typography>
                ))}
              </div>
            </div>
          </BottomSheetContent>
        </BottomSheet>

        {/* Custom Close Icon Sheet */}
        <BottomSheet
          open={activeSheet === "custom-icon"}
          onOpenChange={(open) => !open && closeSheet()}

          title="Custom Close Icon"
          closeIcon={<Heart width={16} height={16} />}
          snapPoints={[25, 60, 100]}
          defaultSnap={60}
        >
          <BottomSheetContent>
            <div style={{ padding: "16px 0" }}>
              <Typography level="bodyMedium" style={{ display: 'block' }}>
                This bottom sheet uses a custom close icon (heart) instead of the default close icon.
                You can use any React element as the close icon.
              </Typography>
            </div>
          </BottomSheetContent>
        </BottomSheet>
      </div>
    )
  },
}

/** BottomSheet with BottomSheetContent and footer */
export const WithFooter: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "BottomSheet using BottomSheetContent component with a footer. Perfect for forms or actions that need a persistent footer area.",
      },
    },
  },
  render: function WithFooterDemo() {
    const [isOpen, setIsOpen] = useState(false)
    const [selectedItems, setSelectedItems] = useState<string[]>([])

    const items = [
      { id: "1", name: "Premium Coffee", price: "$4.99", icon: <Star width={16} height={16} /> },
      { id: "2", name: "Fresh Pastry", price: "$2.99", icon: <Heart width={16} height={16} /> },
      { id: "3", name: "Organic Tea", price: "$3.49", icon: <Shopping width={16} height={16} /> },
    ]

    const toggleItem = (itemId: string) => {
      setSelectedItems(prev =>
        prev.includes(itemId)
          ? prev.filter(id => id !== itemId)
          : [...prev, itemId]
      )
    }

    const footer = (
      <div style={{ display: "flex", gap: 12, padding: "16px 0" }}>
        <Button
          variant="outline"
          onClick={() => setIsOpen(false)}
          style={{ flex: 1 }}
        >
          Cancel
        </Button>
        <Button
          onClick={() => {
            console.log("Selected items:", selectedItems)
            setIsOpen(false)
          }}
          style={{ flex: 1 }}
          disabled={selectedItems.length === 0}
        >
          Add {selectedItems.length} item{selectedItems.length !== 1 ? 's' : ''}
        </Button>
      </div>
    )

    return (
      <div style={{ padding: 20, minHeight: "100vh" }}>
        <Button onClick={() => setIsOpen(true)}>Select Items</Button>

        <BottomSheet
          open={isOpen}
          onOpenChange={setIsOpen}

          title="Select Items"
          snapPoints={[40, 70]}
          defaultSnap={70}
        >
          <BottomSheetContent footer={footer}>
            <div style={{ padding: "16px 0" }}>
              <Typography level="bodyMedium" style={{ marginBottom: 16 }}>
                Choose items to add to your order:
              </Typography>

              <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
                {items.map((item) => (
                  <button
                    key={item.id}
                    onClick={() => toggleItem(item.id)}
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: 12,
                      padding: 12,
                      border: `2px solid ${selectedItems.includes(item.id) ? "var(--apl-alias-color-primary-primary)" : "#eee"}`,
                      borderRadius: 8,
                      cursor: "pointer",
                      backgroundColor: selectedItems.includes(item.id) ? "var(--apl-alias-color-primary-primary-container)" : "transparent",
                      width: "100%",
                      textAlign: "left",
                    }}
                  >
                    {item.icon}
                    <div style={{ flex: 1 }}>
                      <Typography level="bodyMedium" style={{ fontWeight: 600 }}>
                        {item.name}
                      </Typography>
                      <Typography level="bodySmall" style={{ color: "#666" }}>
                        {item.price}
                      </Typography>
                    </div>
                    <div
                      style={{
                        width: 20,
                        height: 20,
                        borderRadius: "50%",
                        border: "2px solid var(--apl-alias-color-primary-primary)",
                        backgroundColor: selectedItems.includes(item.id) ? "var(--apl-alias-color-primary-primary)" : "transparent",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      {selectedItems.includes(item.id) && (
                        <div style={{ width: 8, height: 8, borderRadius: "50%", backgroundColor: "white" }} />
                      )}
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </BottomSheetContent>
        </BottomSheet>
      </div>
    )
  },
}

/** BottomSheet used without BottomSheetContent helper */
export const WithoutBottomSheetContent: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "BottomSheet used without the BottomSheetContent wrapper. Useful when you want full control over layout and don't need the built-in padding/footer behavior.",
      },
    },
  },
  render: function WithoutBottomSheetContentDemo(args) {
    const [isOpen, setIsOpen] = useState(false)

    return (
      <div style={{ padding: 20, minHeight: "100vh" }}>
        <Button onClick={() => setIsOpen(true)}>Open Sheet (no helper)</Button>

        <BottomSheet
          {...args}
          open={isOpen}
          onOpenChange={setIsOpen}

          title="Custom Content"
        >
          {/* Direct children instead of <BottomSheetContent> */}
          <div style={{ padding: "16px 0" }}>
            <Typography level="bodyLarge" style={{ marginBottom: 12 }}>
              Direct children content
            </Typography>
            <Typography level="bodyMedium" style={{ marginBottom: 16, display: 'block' }}>
              This sheet renders plain elements as children without using BottomSheetContent.
            </Typography>
            <div style={{ display: "flex", gap: 8 }}>
              <Input placeholder="Sample field" size="small" fullWidth />
              <Button onClick={() => setIsOpen(false)} size="small">Submit</Button>
            </div>
          </div>
        </BottomSheet>
      </div>
    )
  },
  args: {
    snapPoints: [40, 70],
    defaultSnap: 40,
  },
}



/** BottomSheet with controlled state and multiple sheets */
export const MultipleSheets: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Example demonstrating multiple bottom sheets with controlled state management. Shows how to handle multiple sheets and pass data between them.",
      },
    },
  },
  render: function MultipleSheetsDemo() {
    const [currentSheet, setCurrentSheet] = useState<string | null>(null)
    const [selectedProduct, setSelectedProduct] = useState<any>(null)

    const products = [
      { id: 1, name: "Wireless Headphones", price: "$99.99", rating: 4.5 },
      { id: 2, name: "Smart Watch", price: "$199.99", rating: 4.8 },
      { id: 3, name: "Bluetooth Speaker", price: "$49.99", rating: 4.2 },
    ]

    const openProductDetails = (product: any) => {
      setSelectedProduct(product)
      setCurrentSheet("details")
    }

    const openProductList = () => {
      setCurrentSheet("list")
    }

    const closeSheets = () => {
      setCurrentSheet(null)
      setSelectedProduct(null)
    }

    return (
      <div style={{ padding: 20, minHeight: "100vh" }}>
        <Button onClick={openProductList}>Browse Products</Button>

        {/* Product List Sheet */}
        <BottomSheet
          open={currentSheet === "list"}
          onOpenChange={(open) => !open && closeSheets()}

          title="Products"
          snapPoints={[40, 70]}
          defaultSnap={70}
        >
          <BottomSheetContent>
              <Typography level="bodyMedium" style={{ marginBottom: 16 }}>
                Select a product to view details:
              </Typography>

              <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
                {products.map((product) => (
                  <Button
                    key={product.id}
                    onClick={() => openProductDetails(product)}
                    variant="outline"
                    fullWidth
                    onMouseEnter={(e) => e.currentTarget.style.backgroundColor = "#f5f5f5"}
                    onMouseLeave={(e) => e.currentTarget.style.backgroundColor = "transparent"}
                  >
                    {product.name}
                  </Button>
                ))}
            </div>
          </BottomSheetContent>
        </BottomSheet>

        {/* Product Details Sheet */}
        <BottomSheet
          open={currentSheet === "details"}
          onOpenChange={(open) => !open && closeSheets()}

          title={selectedProduct?.name || "Product Details"}
          snapPoints={[50, 80]}
          defaultSnap={80}
        >
          <BottomSheetContent
            footer={
              <div style={{ display: "flex", gap: 12, padding: "16px 0" }}>
                <Button
                  variant="outline"
                  onClick={openProductList}
                  style={{ flex: 1 }}
                >
                  Back to List
                </Button>
                <Button
                  onClick={() => {
                    console.log("Added to cart:", selectedProduct)
                    closeSheets()
                  }}
                  style={{ flex: 1 }}
                >
                  Add to Cart
                </Button>
              </div>
            }
          >
            {selectedProduct && (
              <div style={{ padding: "16px 0" }}>
                <div style={{ marginBottom: 24 }}>
                  <Typography level="titleLarge" style={{ marginBottom: 8 }}>
                    {selectedProduct.name}
                  </Typography>
                  <Typography level="bodyLarge" style={{ color: "var(--apl-alias-color-primary-primary)", fontWeight: 600, marginBottom: 16 }}>
                    {selectedProduct.price}
                  </Typography>
                  <div style={{ display: "flex", alignItems: "center", gap: 8, marginBottom: 16 }}>
                    <Star width={16} height={16} style={{ color: "var(--apl-alias-color-warning-warning)" }} />
                    <Typography level="bodyMedium">
                      {selectedProduct.rating}/5 rating
                    </Typography>
                  </div>
                </div>

                <div style={{ marginBottom: 24 }}>
                  <Typography level="bodyLarge" style={{ marginBottom: 12, fontWeight: 600 }}>
                    Description
                  </Typography>
                  <Typography level="bodyMedium" style={{ lineHeight: 1.6 }}>
                    This is a high-quality product with excellent features and great customer reviews.
                    Perfect for everyday use with modern design and reliable performance.
                  </Typography>
                </div>

                <div style={{ marginBottom: 24 }}>
                  <Typography level="bodyLarge" style={{ marginBottom: 12, fontWeight: 600 }}>
                    Features
                  </Typography>
                  <ul style={{ paddingLeft: 20, margin: 0 }}>
                    <li style={{ marginBottom: 8 }}>
                      <Typography level="bodyMedium">Premium build quality</Typography>
                    </li>
                    <li style={{ marginBottom: 8 }}>
                      <Typography level="bodyMedium">Long-lasting battery</Typography>
                    </li>
                    <li style={{ marginBottom: 8 }}>
                      <Typography level="bodyMedium">Easy to use interface</Typography>
                    </li>
                    <li>
                      <Typography level="bodyMedium">1-year warranty included</Typography>
                    </li>
                  </ul>
                </div>
              </div>
            )}
          </BottomSheetContent>
        </BottomSheet>
      </div>
    )
  },
}