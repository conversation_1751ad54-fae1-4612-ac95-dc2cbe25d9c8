import { useState, type ReactNode } from "react"
import { Button } from "@apollo/ui"

interface LanguageToggleProps {
  thaiContent: ReactNode
  englishContent: ReactNode
  defaultLanguage?: "th" | "en"
}

export const LanguageToggle = ({
  thaiContent,
  englishContent,
  defaultLanguage = "th",
}: LanguageToggleProps) => {
  const [lang, setLang] = useState(defaultLanguage)

  return (
    <div style={{ position: "relative" }}>
      <div
        style={{
          position: "absolute",
          top: 0,
          right: 0,
          display: "flex",
          gap: "8px",
          zIndex: 10,
        }}
      >
        <Button
          size="small"
          variant={lang === "th" ? "filled" : "outline"}
          onClick={() => setLang("th")}
        >
          🇹🇭 ไทย
        </Button>
        <Button
          size="small"
          variant={lang === "en" ? "filled" : "outline"}
          onClick={() => setLang("en")}
        >
          🇬🇧 EN
        </Button>
      </div>
      <div style={{ paddingTop: "48px" }}>
        {lang === "th" ? thaiContent : englishContent}
      </div>
    </div>
  )
}
