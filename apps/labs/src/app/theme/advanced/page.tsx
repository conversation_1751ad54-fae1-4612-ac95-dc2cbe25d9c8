"use client"

import React, { useState } from "react"
import { ApolloProvider } from "@apollo/ui"
import { Buttons } from "../../components/button"
import { Inputs } from "@/app/components/inputs"
import { SelectDemo } from "@/app/components/select"
import FloatButtons from "@/app/components/float-buttons"
import { AutocompletesExample } from "@/app/components/autocompletes"
import { Checkboxs } from "@/app/components/checkboxs"
import { Textareas } from "@/app/components/textareas"
import IconButtons from "@/app/components/icon-buttons"
import { Switches } from "@/app/components/switch"
import { RadioDemo } from "@/app/components/radio"
import { Chips } from "@/app/components/chips"
import Badges from "@/app/components/badges"
import { Alerts } from "@/app/components/alerts"
import UploadBoxDemo from "@/app/components/upload-box"
import { DateInputs } from "@/app/components/date-inputs"
import { Typographys } from "@/app/components/typographys"
import { SortingIconDemo } from "@/app/components/sorting-icons"
import BreadcrumbsDemo from "@/app/components/breadcrumbs"
import { Paginations } from "@/app/components/pagination"
import { TabsDemo } from "@/app/components/tabs"
import { ModalDemo } from "@/app/components/modals"
import { CapsuleTabs } from "@/app/components/capsule-tabs"
import { AccordionDemo } from "../../components/accordion"
import { Toasts } from "@/app/components/toasts"
import { ComboboxsExample } from "@/app/components/comboboxs"

export default function AdvancedThemePage() {
  const [currentTheme, setCurrentTheme] = useState<"light" | "dark">("light")

  return (
    <ApolloProvider themeProps={{ mode: currentTheme }}>
    {/* <Theme mode={currentTheme}> */}
      <div
        style={{
          backgroundColor: "var(--apl-color-gray-smoke-99, #fafafa)",
          color: "var(--apl-color-gray-smoke-10, #1a1a1a)",
          transition: "all 0.3s ease",
        }}
      >
        <div style={{ padding: "24px", maxWidth: "1200px", margin: "0 auto" }}>
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: "32px",
            }}
          >
            <div style={{ display: "flex", alignItems: "center", gap: "16px" }}>
              <a
                href="/theme"
                style={{
                  color: "var(--apl-color-green-pine-50, #2C8745)",
                  textDecoration: "none",
                  fontSize: "14px",
                }}
              >
                ← Basic Examples
              </a>
              <h1 style={{ margin: 0, fontSize: "28px" }}>
                Advanced Theme Demo
              </h1>
            </div>

            <div style={{ display: "flex", gap: "12px", alignItems: "center" }}>
              <button
                onClick={() =>
                  setCurrentTheme(currentTheme === "light" ? "dark" : "light")
                }
                style={{
                  padding: "8px 16px",
                  borderRadius: "6px",
                  border: "1px solid #ccc",
                  backgroundColor: "var(--apl-color-gray-smoke-100, #fff)",
                  color: "var(--apl-color-gray-smoke-10, #000)",
                  cursor: "pointer",
                }}
              >
                {currentTheme === "light" ? "🌙 Dark" : "☀️ Light"}
              </button>
            </div>
          </div>
          <ComboboxsExample />
          <DateInputs />
          <Toasts/>
          <AccordionDemo />
          <ModalDemo />
          <CapsuleTabs/>
          <TabsDemo />
          <Paginations />
          <BreadcrumbsDemo />
          <SortingIconDemo />
          <Typographys />
          <UploadBoxDemo />
          <Badges />
          <Alerts />
          <Chips />
          <RadioDemo />
          <Textareas />
          <IconButtons />
          <Switches />
          <Buttons />
          <FloatButtons />
          <Inputs />
          <SelectDemo />
          <AutocompletesExample />
          <Checkboxs />
        </div>
      </div>
    {/* </Theme> */}
    </ApolloProvider>
  )
}
