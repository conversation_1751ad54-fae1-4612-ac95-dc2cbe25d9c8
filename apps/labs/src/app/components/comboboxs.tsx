"use client"

import { useState } from "react"
import { Combobox } from "@apollo/ui"

import { ComponentBox, ComponentGroup } from "./common"

const fruits = [
  { label: "Apple", id: "apple" },
  { label: "Banana", id: "banana" },
  { label: "Cherry", id: "cherry" },
  { label: "Dragon Fruit", id: "dragonFruit" },
  { label: "Elderberry", id: "elderberry" },
  { label: "Fig", id: "fig" },
  { label: "Grape", id: "grape" },
  { label: "Honeydew", id: "honeydew" },
]

const countries = [
  { label: "United States", id: "us" },
  { label: "United Kingdom", id: "uk" },
  { label: "Canada", id: "ca" },
  { label: "Australia", id: "au" },
  { label: "Germany", id: "de" },
  { label: "France", id: "fr" },
  { label: "Japan", id: "jp" },
  { label: "Brazil", id: "br" },
]

const categories = [
  { label: "Electronics", id: "electronics" },
  { label: "Clothing", id: "clothing" },
  { label: "Books", id: "books" },
  { label: "Home & Garden", id: "home" },
  { label: "Sports & Outdoors", id: "sports" },
  { label: "Toys & Games", id: "toys" },
]

export function ComboboxsExample() {
  return (
    <ComponentGroup>
      <ComponentBox direction="horizontal">
        <BasicCombobox />
        <SmallCombobox />
        <FullWidthCombobox />
      </ComponentBox>
      <ComponentBox direction="horizontal">
        <ComboboxWithError />
        <RequiredCombobox />
        <DisabledCombobox />
      </ComponentBox>
      <ComponentBox direction="horizontal">
        <ComboboxWithDecorators />
        <ComboboxWithCustomSlots />
        <ControlledCombobox />
      </ComponentBox>
      <ComponentBox direction="horizontal">
        <MultiSelectCombobox />
        <MultiSelectSmallCombobox />
        <MultiSelectFullWidthCombobox />
      </ComponentBox>
      <ComponentBox direction="horizontal">
        <ComboboxWithCustomLabels />
        <MultiSelectWithCustomLabels />
      </ComponentBox>
    </ComponentGroup>
  )
}

// Basic Combobox
function BasicCombobox() {
  const [value, setValue] = useState<string>("")

  return (
    <ComponentBox>
      <h3>Basic Combobox</h3>
      <Combobox
        label="Select a fruit"
        placeholder="Choose a fruit..."
        helperText="Select your favorite fruit"
        options={fruits}
        value={value}
        onValueChange={(val) => setValue(val ?? "")}
      />
      <p style={{ marginTop: "8px", fontSize: "14px", color: "#666" }}>
        Selected: {value || "none"}
      </p>
    </ComponentBox>
  )
}

// Small Size Combobox
function SmallCombobox() {
  const [value, setValue] = useState<string>("")

  return (
    <ComponentBox>
      <h3>Small Size</h3>
      <Combobox
        label="Country"
        placeholder="Select a country..."
        size="small"
        options={countries}
        value={value}
        onValueChange={(val) => setValue(val ?? "")}
      />
      <p style={{ marginTop: "8px", fontSize: "14px", color: "#666" }}>
        Selected: {value || "none"}
      </p>
    </ComponentBox>
  )
}

// Full Width Combobox
function FullWidthCombobox() {
  const [value, setValue] = useState<string>("")

  return (
    <ComponentBox>
      <h3>Full Width</h3>
      <Combobox
        label="Category"
        placeholder="Select a category..."
        helperText="Choose a product category"
        fullWidth
        options={categories}
        value={value}
        onValueChange={(val) => setValue(val ?? "")}
      />
      <p style={{ marginTop: "8px", fontSize: "14px", color: "#666" }}>
        Selected: {value || "none"}
      </p>
    </ComponentBox>
  )
}

// Combobox with Error State
function ComboboxWithError() {
  const [value, setValue] = useState<string>("")

  return (
    <ComponentBox>
      <h3>Error State</h3>
      <Combobox
        label="Country"
        placeholder="Select a country..."
        helperText="Please select a valid country"
        error
        options={countries.slice(0, 4)}
        value={value}
        onValueChange={(val) => setValue(val ?? "")}
      />
    </ComponentBox>
  )
}

// Required Combobox
function RequiredCombobox() {
  const [value, setValue] = useState<string>("")

  return (
    <ComponentBox>
      <h3>Required Field</h3>
      <Combobox
        label="Fruit"
        placeholder="Select a fruit..."
        helperText="This field is required"
        required
        options={fruits.slice(0, 5)}
        value={value}
        onValueChange={(val) => setValue(val ?? "")}
      />
    </ComponentBox>
  )
}

// Disabled Combobox
function DisabledCombobox() {
  return (
    <ComponentBox>
      <h3>Disabled State</h3>
      <Combobox
        label="Disabled Combobox"
        placeholder="This is disabled..."
        helperText="You cannot interact with this field"
        disabled
        options={fruits.slice(0, 3)}
        value="apple"
      />
    </ComponentBox>
  )
}

// Combobox with Decorators
function ComboboxWithDecorators() {
  const [value, setValue] = useState<string>("")

  return (
    <ComponentBox>
      <h3>With Decorators</h3>
      <Combobox
        label="Category"
        placeholder="Select a category..."
        labelDecorator={<span style={{ color: "#0066cc", fontSize: "12px" }}>Optional</span>}
        helperTextDecorator={<span>ℹ️</span>}
        helperText="Choose your preferred category"
        options={categories.slice(0, 4)}
        value={value}
        onValueChange={(val) => setValue(val ?? "")}
      />
    </ComponentBox>
  )
}

// Combobox with Custom Slots
function ComboboxWithCustomSlots() {
  const [value, setValue] = useState<string>("")

  return (
    <ComponentBox>
      <h3>Custom Slots</h3>
      <Combobox
        label="Styled Combobox"
        placeholder="Select an option..."
        options={fruits.slice(0, 4)}
        value={value}
        onValueChange={(val) => setValue(val ?? "")}
        slotProps={{
          container: {
            className: "custom-container",
          },
          popup: {
            className: "custom-popup",
          },
        }}
      />
      <p style={{ marginTop: "8px", fontSize: "12px", color: "#999" }}>
        Uses custom slot props for styling
      </p>
    </ComponentBox>
  )
}

// Controlled Combobox
function ControlledCombobox() {
  const [value, setValue] = useState<string>("banana")

  const handleClear = () => {
    setValue("")
  }

  const handleSetApple = () => {
    setValue("apple")
  }

  return (
    <ComponentBox>
      <h3>Controlled Combobox</h3>
      <Combobox
        label="Controlled Value"
        placeholder="Select a fruit..."
        options={fruits.slice(0, 5)}
        value={value}
        onValueChange={(val) => setValue(val ?? "")}
      />
      <div style={{ marginTop: "12px", display: "flex", gap: "8px" }}>
        <button
          onClick={handleSetApple}
          style={{
            padding: "4px 12px",
            fontSize: "12px",
            border: "1px solid #ddd",
            borderRadius: "4px",
            cursor: "pointer",
          }}
        >
          Set Apple
        </button>
        <button
          onClick={handleClear}
          style={{
            padding: "4px 12px",
            fontSize: "12px",
            border: "1px solid #ddd",
            borderRadius: "4px",
            cursor: "pointer",
          }}
        >
          Clear
        </button>
      </div>
      <p style={{ marginTop: "8px", fontSize: "14px", color: "#666" }}>
        Current: {value || "none"}
      </p>
    </ComponentBox>
  )
}

// Multi-Select Combobox
function MultiSelectCombobox() {
  const [value, setValue] = useState<string[]>([])

  return (
    <ComponentBox>
      <h3>Multi-Select</h3>
      <Combobox
        label="Select multiple fruits"
        placeholder="Choose fruits..."
        helperText="You can select multiple items"
        multiple={true}
        options={fruits}
        value={value}
        onValueChange={(val) => setValue(val ?? [])}
      />
      <p style={{ marginTop: "8px", fontSize: "14px", color: "#666" }}>
        Selected: {value.length > 0 ? value.join(", ") : "none"}
      </p>
    </ComponentBox>
  )
}

// Multi-Select Small Size
function MultiSelectSmallCombobox() {
  const [value, setValue] = useState<string[]>(["us", "uk"])

  return (
    <ComponentBox>
      <h3>Multi-Select Small</h3>
      <Combobox
        label="Countries"
        placeholder="Select countries..."
        multiple={true}
        size="small"
        options={countries}
        value={value}
        onValueChange={(val) => setValue(val ?? [])}
      />
      <p style={{ marginTop: "8px", fontSize: "14px", color: "#666" }}>
        Selected: {value.length} countries
      </p>
    </ComponentBox>
  )
}

// Multi-Select Full Width
function MultiSelectFullWidthCombobox() {
  const [value, setValue] = useState<string[]>([])

  return (
    <ComponentBox>
      <h3>Multi-Select Full Width</h3>
      <Combobox
        label="Categories"
        placeholder="Select categories..."
        helperText="Select one or more categories"
        multiple={true}
        fullWidth
        options={categories}
        value={value}
        onValueChange={(val) => setValue(val ?? [])}
      />
      <p style={{ marginTop: "8px", fontSize: "14px", color: "#666" }}>
        Selected: {value.length} items
      </p>
    </ComponentBox>
  )
}

// Combobox with Custom ReactNode Labels
function ComboboxWithCustomLabels() {
  const [value, setValue] = useState<string>("")

  const customOptions = [
    {
      label: "Apple",
      id: "apple",
      render: (props: any) => (
        <div {...props} style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <span style={{ fontSize: "20px" }}>🍎</span>
          <div>
            <div style={{ fontWeight: 500 }}>Apple</div>
            <div style={{ fontSize: "12px", color: "#666" }}>Sweet and crispy</div>
          </div>
        </div>
      ),
    },
    {
      label: "Banana",
      id: "banana",
      render: (props: any) => (
        <div {...props} style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <span style={{ fontSize: "20px" }}>🍌</span>
          <div>
            <div style={{ fontWeight: 500 }}>Banana</div>
            <div style={{ fontSize: "12px", color: "#666" }}>Rich in potassium</div>
          </div>
        </div>
      ),
    },
    {
      label: "Cherry",
      id: "cherry",
      render: (props: any) => (
        <div {...props} style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <span style={{ fontSize: "20px" }}>🍒</span>
          <div>
            <div style={{ fontWeight: 500 }}>Cherry</div>
            <div style={{ fontSize: "12px", color: "#666" }}>Small and sweet</div>
          </div>
        </div>
      ),
    },
    {
      label: "Mango",
      id: "mango",
      render: (props: any) => (
        <div {...props} style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <span style={{ fontSize: "20px" }}>🥭</span>
          <div>
            <div style={{ fontWeight: 500 }}>Mango</div>
            <div style={{ fontSize: "12px", color: "#666" }}>Tropical delight</div>
          </div>
        </div>
      ),
    },
    {
      label: "Strawberry",
      id: "strawberry",
      render: (props: any) => (
        <div {...props} style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <span style={{ fontSize: "20px" }}>🍓</span>
          <div>
            <div style={{ fontWeight: 500 }}>Strawberry</div>
            <div style={{ fontSize: "12px", color: "#666" }}>Fresh and juicy</div>
          </div>
        </div>
      ),
    },
  ]

  const itemToStringLabel = (val: string) => {
    const labels: Record<string, string> = {
      apple: "Apple",
      banana: "Banana",
      cherry: "Cherry",
      mango: "Mango",
      strawberry: "Strawberry",
    }
    return labels[val] || String(val)
  }

  return (
    <ComponentBox>
      <h3>Custom ReactNode Labels</h3>
      <Combobox
        label="Select a fruit"
        placeholder="Choose your favorite..."
        helperText="Options with custom elements (emoji + description)"
        options={customOptions}
        value={value}
        onValueChange={(val) => setValue(val ?? "")}
        itemToStringLabel={itemToStringLabel}
      />
      <p style={{ marginTop: "8px", fontSize: "14px", color: "#666" }}>
        Selected: {value || "none"}
      </p>
    </ComponentBox>
  )
}

// Multi-Select with Custom ReactNode Labels
function MultiSelectWithCustomLabels() {
  const [value, setValue] = useState<string[]>([])

  const customOptions = [
    {
      label: "United States",
      id: "us",
      render: (props: any) => (
        <div {...props} style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <span style={{ fontSize: "18px" }}>🇺🇸</span>
          <div>
            <div style={{ fontWeight: 500 }}>United States</div>
            <div style={{ fontSize: "11px", color: "#999" }}>North America</div>
          </div>
        </div>
      ),
    },
    {
      label: "United Kingdom",
      id: "uk",
      render: (props: any) => (
        <div {...props} style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <span style={{ fontSize: "18px" }}>🇬🇧</span>
          <div>
            <div style={{ fontWeight: 500 }}>United Kingdom</div>
            <div style={{ fontSize: "11px", color: "#999" }}>Europe</div>
          </div>
        </div>
      ),
    },
    {
      label: "Canada",
      id: "ca",
      render: (props: any) => (
        <div {...props} style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <span style={{ fontSize: "18px" }}>🇨🇦</span>
          <div>
            <div style={{ fontWeight: 500 }}>Canada</div>
            <div style={{ fontSize: "11px", color: "#999" }}>North America</div>
          </div>
        </div>
      ),
    },
    {
      label: "Australia",
      id: "au",
      render: (props: any) => (
        <div {...props} style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <span style={{ fontSize: "18px" }}>🇦🇺</span>
          <div>
            <div style={{ fontWeight: 500 }}>Australia</div>
            <div style={{ fontSize: "11px", color: "#999" }}>Oceania</div>
          </div>
        </div>
      ),
    },
    {
      label: "Japan",
      id: "jp",
      render: (props: any) => (
        <div {...props} style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <span style={{ fontSize: "18px" }}>🇯🇵</span>
          <div>
            <div style={{ fontWeight: 500 }}>Japan</div>
            <div style={{ fontSize: "11px", color: "#999" }}>Asia</div>
          </div>
        </div>
      ),
    },
  ]

  const itemToStringLabel = (val: string) => {
    const labels: Record<string, string> = {
      us: "United States",
      uk: "United Kingdom",
      ca: "Canada",
      au: "Australia",
      jp: "Japan",
    }
    return labels[val] || String(val)
  }

  return (
    <ComponentBox>
      <h3>Multi-Select Custom Labels</h3>
      <Combobox
        label="Select countries"
        placeholder="Choose countries..."
        helperText="Multi-select with flag icons and regions"
        multiple={true}
        options={customOptions}
        value={value}
        onValueChange={(val) => setValue(val ?? [])}
        itemToStringLabel={itemToStringLabel}
      />
      <p style={{ marginTop: "8px", fontSize: "14px", color: "#666" }}>
        Selected: {value.length > 0 ? value.map(itemToStringLabel).join(", ") : "none"}
      </p>
    </ComponentBox>
  )
}
