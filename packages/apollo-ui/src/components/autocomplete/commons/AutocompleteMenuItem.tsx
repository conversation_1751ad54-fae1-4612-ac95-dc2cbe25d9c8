import { Menu } from "@base-ui-components/react/menu"
import classNames from "classnames"

import { Checkbox } from "../../checkbox"
import { MenuItem } from "../../menu-item"

type AutocompleteMenuItemProps<T> = {
  label: string
  value: T
  checkable?: boolean
  selected?: boolean
  indeterminate?: boolean
  className?: string
} & Menu.Item.Props

export function AutocompleteMenuItem<T>({
  label,
  disabled,
  checkable,
  selected,
  indeterminate,
  className,
  ...menuItemProps
}: AutocompleteMenuItemProps<T>) {
  if (checkable) {
    return (
      <Menu.CheckboxItem
        {...menuItemProps}
        disabled={disabled}
        nativeButton={false}
        className={classNames("ApolloAutocomplete-checkboxItem", className)}
        render={(
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          { onMouseMove, onMouseDown, onMouseUp, onPointerLeave, ...props }
        ) => {
          const filteredProps = Object.fromEntries(
            Object.entries(props).filter(
              ([key]) => !key.match(/data-/) && !key.match(/aria-/)
            )
          )

          const dataAttributes = {
            ...(selected ? { "data-checked": "" } : {}),
            ...(!selected ? { "data-unchecked": "" } : {}),
            ...(disabled ? { "data-disabled": "" } : {}),
          }
          return (
            <MenuItem
              startDecoration={
                checkable ? (
                  <Checkbox
                    disabled={disabled}
                    indeterminate={indeterminate}
                    checked={selected}
                  />
                ) : undefined
              }
              aria-checked={selected}
              disabled={disabled}
              selected={selected}
              label={label}
              className={className}
              {...filteredProps}
              {...dataAttributes}
            />
          )
        }}
      />
    )
  }
  return (
    <Menu.Item
      {...menuItemProps}
      disabled={disabled}
      nativeButton={false}
      className={classNames("ApolloAutocomplete-menuItem", className)}
      render={(props) => (
        <MenuItem
          {...props}
          label={label}
          disabled={disabled}
          selected={selected}
          className={className}
        />
      )}
    />
  )
}
