@layer legacy {
  .tabs {
    --apl-tabs-list-border-bottom-color: var(--apl-colors-border-default, #D3D7E1);
    /* tabItem */
    --apl-tabs-item-border-bottom-color: var(--apl-colors-border-primary-subdued, #409261);
    --apl-tabs-item-padding: var(--apl-space-padding-xs, 8px) var(--apl-space-padding-md, 16px);
    --apl-tabs-item-color: var(--apl-colors-content-description);
    --apl-tabs-item-hover-background: var(--apl-colors-surface-static-ui-primary, #f5fff7);
    --apl-tabs-item-disabled-color: var(--apl-colors-content-disabled);
    --apl-tabs-item-selected-color: var(--apl-colors-content-primary-default);
    /* tabsIndicator */
    --apl-tabs-indicator-background-color: var(--apl-colors-border-primary-default, #006d2e);
  }
}

@layer apollo {
  .tabs {
    --apl-tabs-list-border-bottom-color: var(--apl-alias-color-secondary-secondary-container, #ECF0FF);
    /* tabItem */
    --apl-tabs-item-border-bottom-color: var(--apl-alias-color-outline-and-border-outline, #ADABAB);
    --apl-tabs-item-padding: var(--apl-alias-spacing-padding-padding5, 8px) var(--apl-alias-spacing-padding-padding8, 16px);
    --apl-tabs-item-color: var(--apl-alias-color-background-and-surface-on-surface);
    --apl-tabs-item-hover-background: var(--apl-alias-color-background-and-surface-surface, #F8F7F7);
    --apl-tabs-item-disabled-color: var(--apl-alias-color-background-and-surface-text-icon-disabled);
    --apl-tabs-item-selected-color: var(--apl-alias-color-primary-primary, #016E2E);
    /* tabsIndicator */
    --apl-tabs-indicator-background-color: var(--apl-alias-color-primary-primary, #016E2E);
  }
}


.tabsRootFullWidth {
  width: 100%;
}

.tabsList {
  display: flex;
  position: relative;
  width: 100%;
  border-bottom: 1px solid var(--apl-tabs-list-border-bottom-color);
}

.tabItem {
  display: flex;
  justify-content: center;
  align-items: center;

  composes: apl-typography-body-large from '../../base.module.css';


  padding: var(--apl-tabs-item-padding);
  cursor: pointer;
  position: relative;
  border: none;
  background: transparent;
  outline: none;

  flex: 0 0 auto;
  width: fit-content;
  text-align: center;
  color: var(--apl-tabs-item-color);
  border-bottom: 1px solid var(--apl-tabs-item-border-bottom-color);

  min-height: 42px;
  min-width: 80px;
  max-width: 440px;

  composes: apl-transition-all from "../../base.module.css";

  &:not([data-disabled]):hover {
    background: var(--apl-tabs-item-hover-background);
  }

  &[data-disabled] {
    color: var(--apl-tabs-item-disabled-color);
  }

  &[data-selected] {
    color: var(--apl-tabs-item-selected-color);
    border-bottom-color: var(--apl-tabs-indicator-background-color);
  }

}

.tabAlignLeft {
  text-align: left;
  justify-content: flex-start;
}

.tabAlignRight {
  text-align: right;
  justify-content: flex-end;
}

.tabsFillContent {
  width: 100%;
  flex: 1;
}

.tabsIndicator {
  width: var(--active-tab-width);
  position: absolute;
  bottom: 0;

  left: var(--active-tab-left);
  height: 4px;
  background-color: var(--apl-tabs-indicator-background-color);

  composes: apl-transition-all from "../../base.module.css";
  composes: fade-in from "../../base.module.css";
}