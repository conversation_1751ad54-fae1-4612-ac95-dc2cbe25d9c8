import { useState } from "react"
import { Combobox } from "./Combobox"

const fruitOptions = [
  { label: "Apple", id: "apple" },
  { label: "Banana", id: "banana" },
  { label: "Cherry", id: "cherry" },
  { label: "Dragon Fruit", id: "dragonFruit" },
  { label: "Elderberry", id: "elderberry" },
]

/**
 * Basic Combobox Example
 */
export function BasicComboboxExample() {
  const [value, setValue] = useState<string>("")

  return (
    <div style={{ padding: "20px", maxWidth: "400px" }}>
      <Combobox
        label="Select a fruit"
        placeholder="Choose a fruit..."
        helperText="Select your favorite fruit"
        options={fruitOptions}
        value={value}
        onValueChange={(val) => setValue(val ?? "")}
      />
      <p>Selected value: {value || "none"}</p>
    </div>
  )
}

const countryOptions = [
  { label: "United States", id: "us" },
  { label: "United Kingdom", id: "uk" },
  { label: "Canada", id: "ca" },
  { label: "Australia", id: "au" },
]

const categoryOptions = [
  { label: "Electronics", id: "electronics" },
  { label: "Clothing", id: "clothing" },
  { label: "Books", id: "books" },
  { label: "Home & Garden", id: "home" },
]

const sizeOptions = [
  { label: "Small", id: "s" },
  { label: "Medium", id: "m" },
  { label: "Large", id: "l" },
  { label: "Extra Large", id: "xl" },
]

/**
 * Combobox with Error State
 */
export function ComboboxWithErrorExample() {
  const [value, setValue] = useState<string>("")

  return (
    <div style={{ padding: "20px", maxWidth: "400px" }}>
      <Combobox
        label="Country"
        placeholder="Select a country..."
        helperText="Please select a valid country"
        error
        required
        options={countryOptions}
        value={value}
        onValueChange={(val) => setValue(val ?? "")}
      />
    </div>
  )
}

/**
 * Full Width Combobox
 */
export function FullWidthComboboxExample() {
  const [value, setValue] = useState<string>("")

  return (
    <div style={{ padding: "20px" }}>
      <Combobox
        label="Category"
        placeholder="Select a category..."
        fullWidth
        options={categoryOptions}
        value={value}
        onValueChange={(val) => setValue(val ?? "")}
      />
    </div>
  )
}

/**
 * Small Size Combobox
 */
export function SmallComboboxExample() {
  const [value, setValue] = useState<string>("")

  return (
    <div style={{ padding: "20px", maxWidth: "400px" }}>
      <Combobox
        label="Size"
        placeholder="Select size..."
        size="small"
        options={sizeOptions}
        value={value}
        onValueChange={(val) => setValue(val ?? "")}
      />
    </div>
  )
}

const simpleOptions = [
  { label: "Option 1", id: "1" },
  { label: "Option 2", id: "2" },
  { label: "Option 3", id: "3" },
]

/**
 * Disabled Combobox
 */
export function DisabledComboboxExample() {
  return (
    <div style={{ padding: "20px", maxWidth: "400px" }}>
      <Combobox
        label="Disabled Combobox"
        placeholder="This is disabled..."
        disabled
        options={fruitOptions.slice(0, 2)}
        value="apple"
      />
    </div>
  )
}

/**
 * Combobox with Custom Slot Props
 */
export function ComboboxWithSlotsExample() {
  const [value, setValue] = useState<string>("")

  return (
    <div style={{ padding: "20px", maxWidth: "400px" }}>
      <Combobox
        label="Custom Styled Combobox"
        placeholder="Select an option..."
        options={simpleOptions}
        value={value}
        onValueChange={(val) => setValue(val ?? "")}
        slotProps={{
          container: {
            className: "custom-trigger",
          },
          popup: {
            className: "custom-popup",
          },
        }}
      />
    </div>
  )
}

const extendedFruitOptions = [
  ...fruitOptions,
  { label: "Fig", id: "fig" },
  { label: "Grape", id: "grape" },
]

const extendedCountryOptions = [
  ...countryOptions,
  { label: "Germany", id: "de" },
  { label: "France", id: "fr" },
]

const extendedCategoryOptions = [
  ...categoryOptions,
  { label: "Sports & Outdoors", id: "sports" },
  { label: "Toys & Games", id: "toys" },
]

/**
 * Multi-Select Combobox
 */
export function MultiSelectComboboxExample() {
  const [value, setValue] = useState<string[]>([])

  return (
    <div style={{ padding: "20px", maxWidth: "400px" }}>
      <Combobox
        label="Select multiple fruits"
        placeholder="Choose fruits..."
        helperText="You can select multiple items"
        multiple={true}
        options={extendedFruitOptions}
        value={value}
        onValueChange={(val) => setValue(val ?? [])}
      />
      <p style={{ marginTop: "8px", fontSize: "14px", color: "#666" }}>
        Selected: {value.length > 0 ? value.join(", ") : "none"}
      </p>
    </div>
  )
}

/**
 * Multi-Select Small Size
 */
export function MultiSelectSmallComboboxExample() {
  const [value, setValue] = useState<string[]>(["us", "uk"])

  return (
    <div style={{ padding: "20px", maxWidth: "400px" }}>
      <Combobox
        label="Countries"
        placeholder="Select countries..."
        multiple={true}
        size="small"
        options={extendedCountryOptions}
        value={value}
        onValueChange={(val) => setValue(val ?? [])}
      />
      <p style={{ marginTop: "8px", fontSize: "14px", color: "#666" }}>
        Selected: {value.length} countries
      </p>
    </div>
  )
}

/**
 * Multi-Select Full Width
 */
export function MultiSelectFullWidthComboboxExample() {
  const [value, setValue] = useState<string[]>([])

  return (
    <div style={{ padding: "20px" }}>
      <Combobox
        label="Categories"
        placeholder="Select categories..."
        helperText="Select one or more categories"
        multiple={true}
        fullWidth
        options={extendedCategoryOptions}
        value={value}
        onValueChange={(val) => setValue(val ?? [])}
      />
    </div>
  )
}

