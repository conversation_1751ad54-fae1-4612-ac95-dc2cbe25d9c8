import type { HTMLProps, ReactNode } from "react"
import { Combobox as BaseCombobox } from "@base-ui-components/react/combobox"

import type { InputProps } from "../input"

/**
 * Option type for Combobox
 */
export type ComboboxOption = {
  label: string
  id: string
  disabled?: boolean

  renderLabel?: () => ReactNode
}

/**
 * Helper type to determine the value type based on the multiple prop
 */
export type ComboboxValueType<
  ItemValue extends ComboboxOption,
  Multiple extends boolean | undefined,
> = Multiple extends true ? ItemValue[] : ItemValue


export type ComboboxProps<
  ItemValue extends ComboboxOption,
  Multiple extends boolean | undefined = false,
> = {
  ref?: React.RefObject<HTMLDivElement>
  /**
   * Whether multiple items can be selected.
   * @default false
   */
  multiple?: Multiple
  /**
   * Array of options to display in the combobox
   */
  options: ItemValue[]
  /**
   * Callback fired when the selected value changes.
   * - When multiple=false: receives ItemValue | null
   * - When multiple=true: receives ItemValue[] | null
   */
  onValueChange?: (value: ComboboxValueType<ItemValue, Multiple> | null) => void
  size?: "medium" | "small"
  /**
   * Slot props for customizing internal components
   */
  slotProps?: ComboboxSlotProps
  /**
   * When items' ids are not sufficient, converts the id to a string label for display.
   */
  itemToStringLabel?: (itemId: string) => string
} & Pick<
  InputProps,
  | "placeholder"
  | "label"
  | "fullWidth"
  | "disabled"
  | "helperText"
  | "error"
  | "required"
  | "labelDecorator"
  | "helperTextDecorator"
> &
  Omit<
    BaseCombobox.Root.Props<ItemValue, Multiple>,
    "onValueChange" | "children" | "items"
  >

/**
 * Slot props for customizing Combobox sub-components
 */
export type ComboboxSlotProps = {
  container?: {
    className?: string
  }
  input?: {
    className?: string
    render?: (
      props: HTMLProps<HTMLInputElement>,
      state: BaseCombobox.Input.State
    ) => ReactNode
  }
  positioner?: {
    className?: string
  }
  popup?: {
    className?: string
  }
  option?: {
    className?: string
    render?: (
      props: HTMLProps<HTMLDivElement>,
      state: {
        selected: boolean
        highlighted: boolean
        disabled: boolean
      } & BaseCombobox.Item.State
    ) => ReactNode
  }
  chips?: {
    className?: string
  }
  chip?: {
    className?: string
    render?: (
      props: HTMLProps<HTMLDivElement>,
      state: BaseCombobox.Chip.State
    ) => ReactNode
  }
}
