import { useCallback, useEffect, useMemo, useRef } from "react"
import { IconButton, isTextElement, Typography } from "@apollo/ui"
import classNames from "classnames"

import { Close } from "../common/Close"
import styles from "./bottom-sheet.module.css"
import type { BottomSheetProps } from "./BottomSheetProps"

const DEFAULT_CLOSE_ICON = <Close width={16} height={16} />
export function BottomSheet({
  defaultOpen,
  open,
  onOpenChange,
  snapPoints = [30, 60, 90],
  defaultSnap = 60,
  title,
  closeIcon = DEFAULT_CLOSE_ICON,
  allowContentDrag = false,
  children,
  className,
  ...props
}: BottomSheetProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)
  const isDraggingRef = useRef(false)
  const startYRef = useRef(0)
  const startHeightRef = useRef(0)
  const rafIdRef = useRef<number | null>(null)
  const pendingHeightRef = useRef<number | null>(null)

  const currentSnapPoints = useMemo(() => {
    if (Array.isArray(snapPoints)) return snapPoints
    if (typeof snapPoints === "number") return [snapPoints]
    return [50]
  }, [snapPoints])

   /** --- Controlled / Uncontrolled Open --- */
  const isOpen = useMemo(() => {
    return open !== undefined ? open : defaultOpen
  }, [open, defaultOpen])

  /** Hide bottom sheet */
  const handleHide = useCallback((): void => {
    if (onOpenChange) onOpenChange(false)
    if (containerRef.current) {
      containerRef.current.style.height = `${defaultSnap}vh`
    }
  }, [onOpenChange])

  // Update sheet height
  const updateSheetHeight = useCallback((h: number) => {
    const clamped = Math.min(100, Math.max(0, h))
    if (containerRef.current) {
      containerRef.current.style.height = `${clamped}vh`
      containerRef.current.classList.toggle(
        styles.bottomSheetContainerFullscreen,
        clamped === 100
      )
    }
  }, [])

  // Drag handlers
  const handleDragMove = useCallback(
    (e: MouseEvent | TouchEvent) => {
      if (!isDraggingRef.current || !containerRef.current) return
      const currentY =
        "touches" in e ? e.touches[0].pageY : (e as MouseEvent).pageY
      const delta = startYRef.current - currentY
      const newHeight =
        startHeightRef.current + (delta / window.innerHeight) * 100

      // schedule height update on next animation frame for smoother dragging
      pendingHeightRef.current = newHeight
      if (rafIdRef.current == null) {
        rafIdRef.current = window.requestAnimationFrame(() => {
          if (pendingHeightRef.current != null) {
            updateSheetHeight(pendingHeightRef.current)
          }
          rafIdRef.current = null
        })
      }
    },
    [updateSheetHeight]
  )

  const handleDragEnd = useCallback(() => {
    if (!isDraggingRef.current || !containerRef.current) return

    isDraggingRef.current = false

    // cleanup any pending animation frame and hints
    if (rafIdRef.current != null) {
      cancelAnimationFrame(rafIdRef.current)
      rafIdRef.current = null
    }
    pendingHeightRef.current = null
    containerRef.current.style.willChange = ""

    cleanupListeners()

    const currentHeight = parseFloat(containerRef.current.style.height || "0")

    // Snap to closest snap point
    let closest = currentSnapPoints[0]
    let minDiff = Math.abs(currentHeight - currentSnapPoints[0])
    for (const sp of currentSnapPoints) {
      const diff = Math.abs(currentHeight - sp)
      if (diff < minDiff) {
        minDiff = diff
        closest = sp
      }
    }

    // If below smallest snap point, close
    if (
      closest === Math.min(...currentSnapPoints) &&
      currentHeight < Math.min(...currentSnapPoints)
    ) {
      handleHide()
    } else {
      updateSheetHeight(closest)
    }
  }, [currentSnapPoints, handleHide, updateSheetHeight])

  const handleDragStart = useCallback(
    (e: React.MouseEvent | React.TouchEvent) => {
      if (!containerRef.current) return

      isDraggingRef.current = true
      startYRef.current = "touches" in e ? e.touches[0].pageY : e.pageY
      startHeightRef.current = parseFloat(
        containerRef.current.style.height || `${defaultSnap}`
      )

      // performance hint during drag
      containerRef.current.style.willChange = "height"

      // Add global listeners
      document.addEventListener("mousemove", handleDragMove)
      document.addEventListener("mouseup", handleDragEnd)
      document.addEventListener("touchmove", handleDragMove)
      document.addEventListener("touchend", handleDragEnd)
    },
    [handleDragMove, handleDragEnd, defaultSnap]
  )

  const isInteractiveTarget = (target: EventTarget | null): boolean => {
    if (!(target instanceof HTMLElement)) return false
    if (target.closest("[data-nodrag]")) return true
    const interactiveSelectors =
      'a,button,input,textarea,select,label,[role="button"],[role="link"],[role="textbox"],[contenteditable="true"]'
    return !!target.closest(interactiveSelectors)
  }

  const handleContainerDragStart = useCallback(
    (e: React.MouseEvent | React.TouchEvent) => {
      if (!allowContentDrag) return
      if (!containerRef.current) return

      const target = (e.target as EventTarget | null) ?? null
      if (isInteractiveTarget(target)) return

      if (
        contentRef.current &&
        target instanceof Node &&
        contentRef.current.contains(target)
      ) {
        const scroller = contentRef.current
        if (scroller.scrollTop > 0) return
      }

      isDraggingRef.current = true
      startYRef.current = "touches" in e ? e.touches[0].pageY : e.pageY
      startHeightRef.current = parseFloat(
        containerRef.current.style.height || `${defaultSnap}`
      )

      containerRef.current.style.willChange = "height"

      document.addEventListener("mousemove", handleDragMove)
      document.addEventListener("mouseup", handleDragEnd)
      document.addEventListener("touchmove", handleDragMove)
      document.addEventListener("touchend", handleDragEnd)
    },
    [allowContentDrag, handleDragMove, handleDragEnd, defaultSnap]
  )

  // Cleanup helper
  const cleanupListeners = useCallback(() => {
    document.removeEventListener("mousemove", handleDragMove)
    document.removeEventListener("mouseup", handleDragEnd)
    document.removeEventListener("touchmove", handleDragMove)
    document.removeEventListener("touchend", handleDragEnd)
    if (rafIdRef.current != null) {
      cancelAnimationFrame(rafIdRef.current)
      rafIdRef.current = null
    }
    if (containerRef.current) {
      containerRef.current.style.willChange = ""
    }
  }, [handleDragMove, handleDragEnd])

  // Cleanup on unmount
  useEffect(() => {
    return () => cleanupListeners()
  }, [cleanupListeners])

  return (
    <div
      className={classNames(
        "ApolloBottomSheet-root",
        styles.bottomSheetRoot,
        { [styles.bottomSheetDragging]: isDraggingRef.current },
        className
      )}
      {...(isOpen ? { "data-open": true } : { "data-closed": true })}
      {...props}
    >
      <div
        className={classNames(
          "ApolloBottomSheet-backdrop",
          styles.bottomSheetBackdrop
        )}
        onClick={handleHide}
      />
      <div
        ref={containerRef}
        {...(isOpen ? { "data-open": true } : { "data-closed": true })}
        className={classNames(
          "ApolloBottomSheet-container",
          styles.bottomSheetContainer
        )}
        style={{ height: `${defaultSnap}vh` }}
        tabIndex={-1}
        aria-modal="true"
        aria-label="Bottom sheet"
        aria-labelledby="bottom-sheet-title"
        role="dialog"
        onMouseDown={allowContentDrag ? handleContainerDragStart : undefined}
        onTouchStart={allowContentDrag ? handleContainerDragStart : undefined}
        onKeyDown={(event) => {
          if (event.key === "Escape") {
            event.stopPropagation()
            handleHide()
          }
        }}
      >
        <div
          className={classNames(
            "ApolloBottomSheet-headerContainer",
            styles.bottomSheetHeaderContainer
          )}
        >
          <div
            className={classNames(
              "ApolloBottomSheet-dragger",
              styles.bottomSheetDragger
            )}
            onMouseDown={handleDragStart}
            onTouchStart={handleDragStart}
            role="button"
            aria-label="Drag to resize bottom sheet"
          />
          <div
            className={classNames(
              "ApolloBottomSheet-header",
              styles.bottomSheetHeader
            )}
          >
            {title ? (
              isTextElement(title) ? (
                <Typography level="titleMedium">{title}</Typography>
              ) : (
                title
              )
            ) : null}
            {closeIcon ? (
              <IconButton
                className={classNames(
                  "ApolloBottomSheet-closeButton",
                  styles.bottomSheetCloseButton
                )}
                size="small"
                onClick={handleHide}
              >
                {closeIcon}
              </IconButton>
            ) : null}
          </div>
        </div>
        <div
          className={classNames(
            "ApolloBottomSheet-content",
            styles.bottomSheetContent
          )}
          ref={contentRef}
        >
          {children}
        </div>
      </div>
    </div>
  )
}
