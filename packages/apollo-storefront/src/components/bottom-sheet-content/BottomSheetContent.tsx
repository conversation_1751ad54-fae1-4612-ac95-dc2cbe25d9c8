import classNames from "classnames"

import styles from "./bottom-sheet-content.module.css"
import type { BottomSheetContentProps } from "./BottomSheetContentProps"

export function BottomSheetContent({
  children,
  footer,
  className,
  ...props
}: BottomSheetContentProps) {
  return (
    <div
      className={classNames(
        "ApolloBottomSheetContent-root",
        styles.bottomSheetContentRoot,
        className
      )}
      {...props}
    >
      <div
        className={classNames(
          "ApolloBottomSheetContent-content",
          styles.bottomSheetContent
        )}
      >
        {children}
      </div>
      {footer && (
        <div
          className={classNames(
            "ApolloBottomSheetContent-footer",
            styles.bottomSheetFooter
          )}
        >
          {footer}
        </div>
      )}
    </div>
  )
}
